<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class Subject extends Model
{
    protected $fillable = [
        'uuid',
        'name',
        'description',
        'user_id',
        'parent_id',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($subject) {
            if (empty($subject->uuid)) {
                $subject->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Relação com o usuário que criou o assunto
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relação com o assunto pai (para estrutura de árvore)
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Subject::class, 'parent_id');
    }

    /**
     * Relação com os assuntos filhos (para estrutura de árvore)
     */
    public function children(): HasMany
    {
        return $this->hasMany(Subject::class, 'parent_id');
    }

    /**
     * Relação ManyToMany com prompts
     */
    public function prompts(): BelongsToMany
    {
        return $this->belongsToMany(Prompt::class, 'prompt_subject');
    }

    /**
     * Usar UUID como chave de rota
     */
    public function getRouteKeyName()
    {
        return 'uuid';
    }

    /**
     * Scope para buscar apenas assuntos raiz (sem pai)
     */
    public function scopeRoots($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope para buscar assuntos de um usuário específico
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }
}
