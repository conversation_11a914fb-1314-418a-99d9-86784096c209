# QueueAI - Sistema de Processamento de Prompts com IA

Sistema backend Lara<PERSON> para processamento assíncrono de prompts utilizando múltiplos modelos de IA (Ollama, OpenAI, Google AI, OpenRouter).

## 📋 Visão Geral

O QueueAI é uma API REST que permite:
- Envio de prompts de texto com anexos de arquivos
- Processamento assíncrono usando filas (queues)
- Suporte a múltiplos modelos de IA simultaneamente
- Monitoramento de status e estatísticas do sistema
- Gerenciamento de arquivos com Spatie MediaLibrary

## 🚀 Tecnologias

- **Framework**: Laravel 12
- **PHP**: 8.2+
- **Banco de Dados**: SQLite (configurável para MySQL/PostgreSQL)
- **Filas**: Database (configurável para Redis/SQS)
- **Armazenamento**: Spatie MediaLibrary
- **IA**: Ollama (padrão), OpenAI, Google AI, OpenRouter

## 🔧 Configuração

### Variáveis de Ambiente (.env)

```env
# Aplicação
APP_NAME=QueueAI
APP_URL=http://localhost

# Banco de Dados
DB_CONNECTION=sqlite

# Filas
QUEUE_CONNECTION=database

# Serviço de IA padrão
AI_SERVICE_TYPE=ollama

# Configurações Ollama
OLLAMA_API_URL=http://***********/ollama/
OLLAMA_DEFAULT_MODEL=hf.co/THUDM/glm-edge-v-2b-gguf:Q8_0
OLLAMA_TIMEOUT=1800

# Configurações OpenAI (opcional)
OPENAI_API_KEY=your_openai_key
OPENAI_DEFAULT_MODEL=gpt-3.5-turbo

# Configurações Google AI (opcional)
GOOGLE_AI_API_KEY=your_google_key
GOOGLE_AI_DEFAULT_MODEL=gemini-pro

# Configurações OpenRouter (opcional)
OPENROUTER_API_KEY=your_openrouter_key
OPENROUTER_DEFAULT_MODEL=openai/gpt-3.5-turbo
```

## 📡 API Endpoints

### Base URL
```
http://localhost/api
```

### 1. Sistema

#### Status do Sistema
```http
GET /api/system/status
```

**Resposta:**
```json
{
  "success": true,
  "data": {
    "system": "QueueAI",
    "version": "1.0.0",
    "status": "running",
    "ollama": {
      "available": true,
      "url": "http://***********/ollama/"
    },
    "queue": {
      "connection": "database",
      "pending_jobs": 0
    },
    "timestamp": "2025-07-03T21:30:00.000000Z"
  }
}
```

#### Modelos Disponíveis
```http
GET /api/system/models
```

#### Estatísticas
```http
GET /api/system/stats
```

### 2. Prompts

#### Criar Prompt
```http
POST /api/prompts
Content-Type: multipart/form-data
```

**Parâmetros:**
- `content` (string, obrigatório): Texto do prompt (máx. 10.000 caracteres)
- `models` (array, opcional): Lista de modelos para processar
- `attachments` (array, opcional): Arquivos anexos (máx. 5 arquivos, 10MB cada)
- `metadata` (object, opcional): Metadados adicionais

**Exemplo de Payload:**
```json
{
  "content": "Analise esta imagem e descreva o que você vê",
  "models": ["llama2", "codellama"],
  "metadata": {
    "user_id": 123,
    "category": "image_analysis"
  }
}
```

**Resposta:**
```json
{
  "success": true,
  "data": {
    "uuid": "550e8400-e29b-41d4-a716-************",
    "status": "pending",
    "models": ["llama2", "codellama"],
    "created_at": "2025-07-03T21:30:00.000000Z"
  },
  "message": "Prompt criado e enviado para processamento."
}
```

#### Consultar Prompt
```http
GET /api/prompts/{uuid}
```

**Resposta:**
```json
{
  "success": true,
  "data": {
    "uuid": "550e8400-e29b-41d4-a716-************",
    "content": "Analise esta imagem e descreva o que você vê",
    "status": "completed",
    "metadata": {
      "user_id": 123,
      "category": "image_analysis"
    },
    "attachments": [
      {
        "id": 1,
        "name": "image.jpg",
        "file_name": "image.jpg",
        "mime_type": "image/jpeg",
        "size": 1024000,
        "url": "http://localhost/storage/1/image.jpg"
      }
    ],
    "responses": [
      {
        "uuid": "660e8400-e29b-41d4-a716-************",
        "model": "llama2",
        "content": "Vejo uma paisagem montanhosa...",
        "status": "completed",
        "metadata": {
          "processing_time": 15.5,
          "service_type": "ollama"
        },
        "generated_at": "2025-07-03T21:31:00.000000Z"
      }
    ],
    "created_at": "2025-07-03T21:30:00.000000Z",
    "processed_at": "2025-07-03T21:31:00.000000Z"
  }
}
```

#### Status do Prompt
```http
GET /api/prompts/{uuid}/status
```

**Resposta:**
```json
{
  "success": true,
  "data": {
    "uuid": "550e8400-e29b-41d4-a716-************",
    "status": "completed",
    "responses_count": 2,
    "processed_at": "2025-07-03T21:31:00.000000Z"
  }
}
```

#### Respostas do Prompt
```http
GET /api/prompts/{uuid}/responses
```

## 📊 Status dos Prompts

- `pending`: Aguardando processamento
- `processing`: Em processamento
- `completed`: Processamento concluído
- `failed`: Falha no processamento

## 📁 Tipos de Arquivo Suportados

- **Imagens**: JPEG, PNG, GIF
- **Documentos**: PDF, TXT, DOC, DOCX
- **Limite**: 5 arquivos por prompt, máximo 10MB cada

## 🔄 Processamento Assíncrono

O sistema utiliza filas Laravel para processamento assíncrono:

1. Prompt é criado com status `pending`
2. Jobs são disparados para cada modelo solicitado
3. Status muda para `processing` durante execução
4. Respostas são salvas conforme completadas
5. Status final: `completed` ou `failed`

## 🏗️ Estrutura do Banco de Dados

### Tabela: prompts
- `id`: ID único
- `uuid`: UUID público
- `content`: Conteúdo do prompt
- `status`: Status atual
- `metadata`: Metadados JSON
- `processed_at`: Data de conclusão
- `created_at`, `updated_at`: Timestamps

### Tabela: responses
- `id`: ID único
- `uuid`: UUID público
- `prompt_id`: Referência ao prompt
- `model`: Nome do modelo usado
- `content`: Resposta gerada
- `metadata`: Metadados JSON
- `status`: Status da resposta
- `generated_at`: Data de geração
- `created_at`, `updated_at`: Timestamps

### Tabela: media (Spatie MediaLibrary)
- Gerencia arquivos anexados aos prompts
- Suporte a conversões (thumbnails)
- URLs públicas para acesso

## 🔐 Autenticação

Atualmente o sistema não possui autenticação ativa. Para implementar:

1. Descomente middleware `auth:sanctum` nas rotas necessárias
2. Configure Laravel Sanctum
3. Implemente sistema de usuários

## 🚀 Instalação e Execução

```bash
# Instalar dependências
composer install

# Configurar ambiente
cp .env.example .env
php artisan key:generate

# Executar migrações
php artisan migrate

# Iniciar servidor
php artisan serve

# Executar worker de filas (em terminal separado)
php artisan queue:work
```

## 📝 Exemplos de Uso

### Envio de Prompt Simples
```bash
curl -X POST http://localhost/api/prompts \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Explique o que é inteligência artificial",
    "models": ["llama2"]
  }'
```

### Envio com Arquivo
```bash
curl -X POST http://localhost/api/prompts \
  -F "content=Analise esta imagem" \
  -F "attachments[]=@/path/to/image.jpg" \
  -F "models[]=llama2"
```

### Consulta de Status
```bash
curl http://localhost/api/prompts/550e8400-e29b-41d4-a716-************/status
```

## 🔧 Configurações Avançadas

### Modelos Suportados
- Ollama: Qualquer modelo instalado localmente
- OpenAI: gpt-3.5-turbo, gpt-4, etc.
- Google AI: gemini-pro, gemini-pro-vision
- OpenRouter: Acesso a múltiplos modelos

### Timeouts e Limites
- Timeout padrão: 1800 segundos (30 minutos)
- Tentativas por job: 3
- Tamanho máximo de prompt: 10.000 caracteres

## 📞 Suporte

Para dúvidas sobre integração ou problemas técnicos, consulte:
- Logs do sistema: `storage/logs/laravel.log`
- Status da fila: `php artisan queue:monitor`
- Documentação Laravel: https://laravel.com/docs
